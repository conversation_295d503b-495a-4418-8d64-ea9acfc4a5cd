from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>
from yunfu.common import ConfigUtils

from backend.fusion.utils.mysql_util import MysqlUtil
from backend.fusion.yky_knowledge_fusion import KnowledgeFusion
from backend.graph.neo4j_utils import Neo4jUtils
from backend.models import UpdateTask
from backend.schemas import KnowledgeFusionParams, TripleFusionParams

logger = YfLogger(__name__)

conf = ConfigUtils.load('conf/config.yaml')
mysql_util = MysqlUtil()
neo4j_utils = Neo4jUtils(conf)


class KnowledgeFusionPipeline:
    knowledge_fusion = KnowledgeFusion(conf)

    @classmethod
    def run(cls, params: KnowledgeFusionParams) -> None:
        task = mysql_util.get_update_task_by_id(params.task_id)
        cls.knowledge_fusion.fuse(task, params.fusion_type, params.synonym_dictory_id, params.sim_threshold)
        if params.fusion_type == 'ontology':
            task.status = 3
        else:
            task.status = 5
        task.save()


class TripleFusionPipeline:
    knowledge_fusion = KnowledgeFusion(conf)

    @classmethod
    def run(cls, params: TripleFusionParams) -> None:
        task = mysql_util.get_update_task_by_id(params.task_id)
        fusion_kg_id = task.update_kg.id
        base_kg_id = task.kg.id
        base_kg = f'KG{base_kg_id}'
        fusion_kg = f'KG{fusion_kg_id}'
        try:
            if params.fusion_type is None:
                cls.knowledge_fusion.triple_fusion(base_kg, fusion_kg, params.task_id, "ontology")
                cls.knowledge_fusion.triple_fusion(base_kg, fusion_kg, params.task_id, "entity")
            else:
                cls.knowledge_fusion.triple_fusion(base_kg, fusion_kg, params.task_id, params.fusion_type)
            neo4j_utils.update_node_type(base_kg)
        except Exception as e:
            logger.error(f'融合失败 {e}')
            task = mysql_util.get_update_task_by_id(params.task_id)
            task._task_status = UpdateTask.Status.FAILURE
            task.save()
