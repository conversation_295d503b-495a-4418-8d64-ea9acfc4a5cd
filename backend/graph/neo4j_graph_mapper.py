import time
from typing import List, Optional

from yunfu.common import LogUtils, yfid
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import Neo4jGraphDb
from yunfu.db.graph.models import Edge, Node

from backend.fusion.schema import EntityTriples

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class Neo4jGraphMapper(BaseGraphMapper):
    graph_db: Neo4jGraphDb

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = Neo4jGraphDb(
            Config(db=db_config, version={"enabled": enable_version})  # type: ignore
        )

    def get_node_by_eid(self, space: str, eid: str) -> Optional[Node]:
        """根据节点_eid获取节点"""
        graph = self.graph_db.get_graph(space)
        nodes = graph.nodes.match(props=[("_eid", "=", eid)]).limit(1).all()
        return nodes[0] if nodes else None

    def get_nodes_by_label(self, space: str, skip: int, limit: int) -> List[Node]:
        """根据标签获取节点列表"""
        graph = self.graph_db.get_graph(space)
        return graph.nodes.match().skip(skip).limit(limit).all()

    def get_edges_by_head_eid(self, space: str, eid: str) -> List[Edge]:
        """根据head的_eid查关系"""
        graph = self.graph_db.get_graph(space)
        return graph.edges.match(src_props=[("_eid", "=", eid)]).all()

    def update_attributes(self, space: str, entity: EntityTriples) -> Node:
        """更新实体属性"""
        graph = self.graph_db.get_graph(space)
        eid = entity.head.e_id
        node = self.get_node_by_eid(space, eid)
        props = {}
        for attribute in entity.attributes:
            props[attribute.attribute_name] = attribute.attribute_value
        return graph.update_node(node.id, props)

    def create_relation(self, space: str, head_eid: str, tail_eid: str, relation_name: str) -> Edge:
        """创建关系"""
        graph = self.graph_db.get_graph(space)
        head_node = self.get_node_by_eid(space, head_eid)
        tail_node = self.get_node_by_eid(space, tail_eid)
        relation_type = '属于' if relation_name == '属于' else '关联'
        rid = yfid(f'{relation_name}{time.time()}')[:6]
        props = {
            'name': relation_name,
            'c': True,
            '_rid': rid,
        }
        edge = Edge(
            id=rid,
            src_id=head_node.id,
            dst_id=tail_node.id,
            type=relation_type,
            props=props
        )
        graph.insert_edge(edge)
        return graph.get_edge(rid)

    def update_node_type(self, space: str) -> None:
        """更新节点类型"""
        graph = self.graph_db.get_graph(space)
        query = f'''
        MATCH (n:{space})-[r]->(m:{space})
        WHERE r.name='属于'
        SET n._type=m.name
        '''
        graph.client.run(query)
