import time
from typing import List, Optional, Union

from yunfu.common import LogUtils, yfid
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb
from yunfu.db.graph.models import Edge, Node

from backend.fusion.schema import EntityTriples

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class NebulaGraphMapper(BaseGraphMapper):

    graph_db: NebulaGraphDb
    # Nebula 暂时不支持下划线前缀，所以记录下带下划线的属性
    node_private_props = ["show_name", "type", "eid", "create_time", "update_time"]
    edge_private_props = ["rid", "create_time", "update_time"]

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = NebulaGraphDb(
            Config(db=db_config, version={"enabled": enable_version})
        )

    def get_node_by_eid(self, space: str, eid: str) -> Optional[Node]:
        """根据节点_eid获取节点"""
        graph = self.graph_db.get_graph(space)
        nodes = graph.nodes.match(props=[("eid", "=", eid)]).limit(1).all()
        if nodes:
            node = nodes[0]
            return self._restore_node_props(node)
        return None

    def get_nodes_by_label(self, space: str, skip: int, limit: int) -> List[Node]:
        """根据标签获取节点列表"""
        graph = self.graph_db.get_graph(space)
        nodes = graph.nodes.match().skip(skip).limit(limit).all()
        return [self._restore_node_props(node) for node in nodes]

    def get_edges_by_head_eid(self, space: str, eid: str) -> List[Edge]:
        """根据head的_eid查关系"""
        graph = self.graph_db.get_graph(space)
        # 先找到头节点
        edges = graph.edges.match(src_props=[("eid", "=", eid)]).all()
        return [self._restore_edge_props(edge) for edge in edges]

    def update_attributes(self, space: str, entity: EntityTriples) -> Node:
        """更新实体属性"""
        graph = self.graph_db.get_graph(space)
        eid = entity.head.e_id
        node = self.get_node_by_eid(space, eid)
        props = {}
        for attribute in entity.attributes:
            props[attribute.attribute_name] = attribute.attribute_value
        node = graph.update_node(node.id, props)
        return self._restore_node_props(node)

    def create_relation(self, space: str, head_eid: str, tail_eid: str, relation_name: str) -> Edge:
        """创建关系"""
        graph = self.graph_db.get_graph(space)
        head_node = self.get_node_by_eid(space, head_eid)
        tail_node = self.get_node_by_eid(space, tail_eid)
        rid = yfid(f'{relation_name}{time.time()}')[:6]
        props = {
            'name': relation_name,
            'c': True,
            'rid': rid
        }
        edge = Edge(
            id=rid,
            src_id=head_node.id,
            dst_id=tail_node.id,
            type=relation_name,
            props=props
        )
        graph.insert_edge(edge)
        edge = graph.get_edge(rid)
        return self._restore_edge_props(edge)

    def update_node_type(self, space: str) -> None:
        """更新节点类型"""
        graph = self.graph_db.get_graph(space)
        ngql = f'''
        MATCH (n:{space})-[r]->(m:{space})
        WHERE r.name == '属于'
        RETURN n
        '''
        nodes = graph.data_wrapper.as_nodes(graph.client.run(ngql).data, output='n')
        for node in nodes:
            node_id = node.id
            node_props = node.props
            node_props['type'] = node_props['name']
            graph.update_node(node_id, node_props)

    def _restore_props(
        self, node_or_edge: Union[Node, Edge], private_props: List[str]
    ) -> Union[Node, Edge]:
        props = {}
        for key, value in node_or_edge.props.items():
            if key in private_props:
                props[f"_{key}"] = value
            else:
                props[key] = value
        node_or_edge.props = props
        return node_or_edge

    def _restore_node_props(self, node: Node) -> Node:
        return self._restore_props(node, self.node_private_props)

    def _restore_edge_props(self, edge: Edge) -> Edge:
        edge.src_node = self._restore_node_props(edge.src_node)
        edge.dst_node = self._restore_node_props(edge.dst_node)
        return self._restore_props(edge, self.edge_private_props)
