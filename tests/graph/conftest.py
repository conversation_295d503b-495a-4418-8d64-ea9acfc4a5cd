import pytest
from yunfu.db.graph.models import Space


@pytest.fixture(scope="session")
def test_space_name():
    return "KGFUSION"


@pytest.fixture(scope="session")
def test_space(test_space_name):
    return Space(
        name=test_space_name,
        props=[
            {"name": "name", "type": "string"},
            {"name": "test_show_name", "type": "string"},
            {"name": "type", "type": "string"},
            {"name": "eid", "type": "string"},
            {"name": "create_time", "type": "string"},
            {"name": "update_time", "type": "string"},
        ],
    )
